// Auth types
export type AdminUser = {
  id: number
  username: string
  email?: string
  nickname?: string
  is_active: boolean
  is_super: boolean
  created_time: string
  updated_time: string
  last_login_time?: string
}

export type LoginRequest = {
  username: string
  password: string
}

export type LoginResponse = {
  access_token: string
  token_type: string
}

// Access Token types
export type TokenStatus = 'ACTIVE' | 'EXPIRED' | 'DISABLED'

export type AccessToken = {
  id: number
  token: string
  name: string
  status: TokenStatus
  max_usage_count?: number
  current_usage_count: number
  expire_time?: string
  max_daily_usage?: number
  max_hourly_usage?: number
  client_ip?: string
  notes?: string
  created_time: string
  updated_time: string
  created_by: AdminUser
}

export type AccessTokenCreate = {
  name: string
  max_usage_count?: number
  expire_time?: string
  max_daily_usage?: number
  max_hourly_usage?: number
  client_ip?: string
  notes?: string
}

export type AccessTokenUpdate = Partial<AccessTokenCreate>

export type TokenValidationRequest = {
  token: string
}

export type TokenValidationResponse = {
  valid: boolean
  token_info?: AccessToken
  message?: string
}

export type Token = {
  id: number
  name: string
  max_usage: number
  current_usage: number
  status: TokenStatus
  expire_time?: string
}

// Extraction types
export type ExtractionStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'DUPLICATE'

export type ExtractionRecord = {
  id: number
  url: string
  title?: string
  content?: string
  author?: string
  publish_time?: string
  status: ExtractionStatus
  error_message?: string
  extraction_time: string
  access_token: AccessToken
  client_ip: string
  user_agent?: string
}

export type ExtractionRequest = {
  token: string
  url: string
  force_refresh?: boolean
}

export type ExtractionResponse = {
  success: boolean
  message: string
  data?: ExtractionRecord
  is_duplicate?: boolean
  error_code?: string
}

export type BatchExtractionRequest = {
  token: string
  urls: string[]
  force_refresh?: boolean
}

export type BatchExtractionResponse = {
  success: boolean
  message: string
  results: ExtractionResponse[]
  total_count: number
  success_count: number
  failed_count: number
}

export type ExtractionHistoryRequest = {
  token: string
  page?: number
  size?: number
  status?: ExtractionStatus
  start_date?: string
  end_date?: string
}

export type ExtractionHistoryResponse = {
  items: ExtractionRecord[]
  total: number
  page: number
  size: number
  pages: number
}

// Extraction Core types
export type CoreStatus = 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'ERROR'

export type ExtractionCore = {
  id: number
  name: string
  api_endpoint: string
  auth_token: string
  status: CoreStatus
  max_concurrent_requests: number
  current_requests: number
  last_health_check?: string
  is_healthy?: boolean
  description?: string
  create_time: string
  update_time: string
}

export type ExtractionCoreCreate = {
  name: string
  api_endpoint: string
  auth_token: string
  max_concurrent_requests?: number
  description?: string
}

export type ExtractionCoreUpdate = Partial<ExtractionCoreCreate>

// Statistics types
export type TokenUsageStats = {
  total_requests: number
  successful_requests: number
  failed_requests: number
  today_requests: number
  this_hour_requests: number
  success_rate: number
  avg_response_time: number
}

export type ExtractionStats = {
  total_extractions: number
  success_extractions: number
  failed_extractions: number
  duplicate_extractions: number
  today_extractions: number
  success_rate: number
  average_response_time: number
  unique_articles: number
  top_authors: Array<{ author: string; count: number }>
  daily_stats: Record<string, { total: number; success: number; failed: number; duplicate: number }>
}

// API Response wrapper
export type ApiResponse<T> = {
  result: T
  message?: string
  code?: number
}

// Pagination
export type PaginationParams = {
  page?: number
  size?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export type PaginatedResponse<T> = {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Form types
export type FormState = {
  loading: boolean
  error: string | null
  success: boolean
}

// Navigation types
export type NavItem = {
  name: string
  path: string
  icon?: string
  children?: NavItem[]
}
