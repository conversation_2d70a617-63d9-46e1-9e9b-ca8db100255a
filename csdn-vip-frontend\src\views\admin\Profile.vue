<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">个人资料</h1>
      <p class="text-gray-600">管理您的个人信息和账户设置</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Profile form -->
      <div class="card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
        
        <form @submit.prevent="updateProfile" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
            <input
              :value="authStore.user?.username"
              type="text"
              disabled
              class="input bg-gray-50"
            />
            <p class="text-xs text-gray-500 mt-1">用户名不可修改</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <input
              v-model="profileForm.email"
              type="email"
              class="input"
              placeholder="邮箱地址"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">昵称</label>
            <input
              v-model="profileForm.nickname"
              type="text"
              class="input"
              placeholder="昵称"
            />
          </div>
          
          <div v-if="profileError" class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">更新失败</h3>
                <div class="mt-2 text-sm text-red-700">{{ profileError }}</div>
              </div>
            </div>
          </div>
          
          <div v-if="profileSuccess" class="rounded-md bg-green-50 p-4">
            <div class="flex">
              <CheckCircleIcon class="h-5 w-5 text-green-400" />
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">更新成功</h3>
                <div class="mt-2 text-sm text-green-700">个人信息已更新</div>
              </div>
            </div>
          </div>
          
          <button
            type="submit"
            :disabled="profileLoading"
            class="btn btn-primary w-full"
          >
            {{ profileLoading ? '更新中...' : '更新信息' }}
          </button>
        </form>
      </div>

      <!-- Password change form -->
      <div class="card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">修改密码</h2>
        
        <form @submit.prevent="changePassword" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">当前密码</label>
            <input
              v-model="passwordForm.currentPassword"
              type="password"
              required
              class="input"
              placeholder="当前密码"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
            <input
              v-model="passwordForm.newPassword"
              type="password"
              required
              class="input"
              placeholder="新密码"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
            <input
              v-model="passwordForm.confirmPassword"
              type="password"
              required
              class="input"
              placeholder="确认新密码"
            />
          </div>
          
          <div v-if="passwordError" class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">修改失败</h3>
                <div class="mt-2 text-sm text-red-700">{{ passwordError }}</div>
              </div>
            </div>
          </div>
          
          <div v-if="passwordSuccess" class="rounded-md bg-green-50 p-4">
            <div class="flex">
              <CheckCircleIcon class="h-5 w-5 text-green-400" />
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">修改成功</h3>
                <div class="mt-2 text-sm text-green-700">密码已更新</div>
              </div>
            </div>
          </div>
          
          <button
            type="submit"
            :disabled="passwordLoading"
            class="btn btn-primary w-full"
          >
            {{ passwordLoading ? '修改中...' : '修改密码' }}
          </button>
        </form>
      </div>
    </div>

    <!-- Account info -->
    <div class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">账户信息</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <dt class="text-sm font-medium text-gray-500">用户ID</dt>
          <dd class="text-sm text-gray-900 mt-1">{{ authStore.user?.id }}</dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500">账户状态</dt>
          <dd class="mt-1">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="{
                'bg-green-100 text-green-800': authStore.user?.is_active,
                'bg-red-100 text-red-800': !authStore.user?.is_active
              }"
            >
              {{ authStore.user?.is_active ? '活跃' : '禁用' }}
            </span>
          </dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500">权限级别</dt>
          <dd class="mt-1">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="{
                'bg-purple-100 text-purple-800': authStore.user?.is_super,
                'bg-blue-100 text-blue-800': !authStore.user?.is_super
              }"
            >
              {{ authStore.user?.is_super ? '超级管理员' : '管理员' }}
            </span>
          </dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500">创建时间</dt>
          <dd class="text-sm text-gray-900 mt-1">
            {{ authStore.user?.created_time ? formatDate(authStore.user.created_time) : 'N/A' }}
          </dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500">最后登录</dt>
          <dd class="text-sm text-gray-900 mt-1">
            {{ authStore.user?.last_login_time ? formatDate(authStore.user.last_login_time) : '从未登录' }}
          </dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500">更新时间</dt>
          <dd class="text-sm text-gray-900 mt-1">
            {{ authStore.user?.updated_time ? formatDate(authStore.user.updated_time) : 'N/A' }}
          </dd>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const profileLoading = ref(false)
const profileError = ref<string | null>(null)
const profileSuccess = ref(false)

const passwordLoading = ref(false)
const passwordError = ref<string | null>(null)
const passwordSuccess = ref(false)

const profileForm = reactive({
  email: '',
  nickname: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const updateProfile = async () => {
  profileLoading.value = true
  profileError.value = null
  profileSuccess.value = false
  
  try {
    const success = await authStore.updateProfile({
      email: profileForm.email || undefined,
      nickname: profileForm.nickname || undefined
    })
    
    if (success) {
      profileSuccess.value = true
      setTimeout(() => {
        profileSuccess.value = false
      }, 3000)
    } else {
      profileError.value = authStore.error || '更新失败'
    }
  } catch (error) {
    profileError.value = '更新失败'
  } finally {
    profileLoading.value = false
  }
}

const changePassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordError.value = '新密码确认不匹配'
    return
  }
  
  passwordLoading.value = true
  passwordError.value = null
  passwordSuccess.value = false
  
  try {
    const success = await authStore.changePassword(
      passwordForm.currentPassword,
      passwordForm.newPassword
    )
    
    if (success) {
      passwordSuccess.value = true
      // Clear form
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      
      setTimeout(() => {
        passwordSuccess.value = false
      }, 3000)
    } else {
      passwordError.value = authStore.error || '密码修改失败'
    }
  } catch (error) {
    passwordError.value = '密码修改失败'
  } finally {
    passwordLoading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  // Initialize form with current user data
  if (authStore.user) {
    profileForm.email = authStore.user.email || ''
    profileForm.nickname = authStore.user.nickname || ''
  }
})
</script>
