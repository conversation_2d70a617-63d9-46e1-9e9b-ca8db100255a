<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">提取核心管理</h1>
        <p class="text-gray-600">管理文章提取核心服务</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="btn btn-primary"
      >
        添加核心
      </button>
    </div>

    <!-- Cores table -->
    <div class="card">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                核心信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                负载情况
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                健康检查
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"></div>
                  加载中...
                </div>
              </td>
            </tr>
            
            <tr v-else-if="cores.length === 0">
              <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                暂无提取核心
              </td>
            </tr>
            
            <tr v-else v-for="core in cores" :key="core.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ core.name }}</div>
                  <div class="text-sm text-gray-500 font-mono">{{ core.api_endpoint }}</div>
                  <div v-if="core.description" class="text-xs text-gray-400 mt-1">{{ core.description }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="{
                    'bg-green-100 text-green-800': core.status === 'ACTIVE',
                    'bg-red-100 text-red-800': core.status === 'INACTIVE',
                    'bg-blue-100 text-blue-800': core.status === 'MAINTENANCE',
                    'bg-yellow-100 text-yellow-800': core.status === 'ERROR'
                  }"
                >
                  {{ getStatusText(core.status) }}
                </span>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  <div class="flex items-center">
                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div
                        class="h-2 rounded-full"
                        :class="{
                          'bg-green-500': core.current_requests / core.max_concurrent_requests < 0.7,
                          'bg-yellow-500': core.current_requests / core.max_concurrent_requests >= 0.7 && core.current_requests / core.max_concurrent_requests < 0.9,
                          'bg-red-500': core.current_requests / core.max_concurrent_requests >= 0.9
                        }"
                        :style="{ width: `${(core.current_requests / core.max_concurrent_requests) * 100}%` }"
                      ></div>
                    </div>
                    <span class="text-xs">
                      {{ core.current_requests }}/{{ core.max_concurrent_requests }}
                    </span>
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>
                  <div>{{ core.last_health_check ? formatDate(core.last_health_check) : '未检查' }}</div>
                  <div class="text-xs" :class="core.is_healthy ? 'text-green-600' : 'text-red-600'">
                    {{ core.is_healthy ? '健康' : '异常' }}
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                <button
                  @click="healthCheck(core)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  健康检查
                </button>
                <button
                  @click="editCore(core)"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  编辑
                </button>
                <button
                  @click="deleteCore(core)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div
      v-if="showCreateModal || editingCore"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ editingCore ? '编辑提取核心' : '添加提取核心' }}
        </h3>
        
        <form @submit.prevent="saveCore" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">名称</label>
            <input
              v-model="coreForm.name"
              type="text"
              required
              class="input"
              placeholder="提取核心名称"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">端点地址</label>
            <input
              v-model="coreForm.api_endpoint"
              type="url"
              required
              class="input"
              placeholder="http://localhost:8000"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">授权令牌</label>
            <input
              v-model="coreForm.auth_token"
              type="text"
              required
              class="input"
              placeholder="授权令牌"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">最大并发请求数</label>
            <input
              v-model.number="coreForm.max_concurrent_requests"
              type="number"
              min="1"
              class="input"
              placeholder="10"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              v-model="coreForm.description"
              rows="3"
              class="input"
              placeholder="可选描述信息"
            ></textarea>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { extractionCoreAPI } from '@/services/api'
import type { ExtractionCore, ExtractionCoreCreate, CoreStatus } from '@/types'

const loading = ref(false)
const saving = ref(false)
const cores = ref<ExtractionCore[]>([])
const showCreateModal = ref(false)
const editingCore = ref<ExtractionCore | null>(null)

const coreForm = reactive<ExtractionCoreCreate>({
  name: '',
  api_endpoint: '',
  auth_token: '',
  max_concurrent_requests: 10,
  description: ''
})

const loadCores = async () => {
  loading.value = true
  
  try {
    const response = await extractionCoreAPI.list()
    cores.value = response.data.result.items || []
  } catch (error) {
    console.error('Failed to load cores:', error)
  } finally {
    loading.value = false
  }
}

const editCore = (core: ExtractionCore) => {
  editingCore.value = core
  Object.assign(coreForm, {
    name: core.name,
    api_endpoint: core.api_endpoint,
    auth_token: core.auth_token,
    max_concurrent_requests: core.max_concurrent_requests,
    description: core.description
  })
}

const closeModal = () => {
  showCreateModal.value = false
  editingCore.value = null
  Object.assign(coreForm, {
    name: '',
    api_endpoint: '',
    auth_token: '',
    max_concurrent_requests: 10,
    description: ''
  })
}

const saveCore = async () => {
  saving.value = true
  
  try {
    if (editingCore.value) {
      await extractionCoreAPI.update(editingCore.value.id, coreForm)
    } else {
      await extractionCoreAPI.create(coreForm)
    }
    
    closeModal()
    await loadCores()
  } catch (error) {
    console.error('Failed to save core:', error)
  } finally {
    saving.value = false
  }
}

const deleteCore = async (core: ExtractionCore) => {
  if (!confirm(`确定要删除提取核心 "${core.name}" 吗？`)) return
  
  try {
    await extractionCoreAPI.delete(core.id)
    await loadCores()
  } catch (error) {
    console.error('Failed to delete core:', error)
  }
}

const healthCheck = async (core: ExtractionCore) => {
  try {
    await extractionCoreAPI.healthCheck(core.id, {
      core_id: core.id,
      timeout: 10
    })
    await loadCores()
    // Could show a success message
  } catch (error) {
    console.error('Health check failed:', error)
    // Could show an error message
  }
}

const getStatusText = (status: CoreStatus) => {
  const statusMap: Record<CoreStatus, string> = {
    ACTIVE: '正常',
    INACTIVE: '离线',
    MAINTENANCE: '维护中',
    ERROR: '错误'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadCores()
})
</script>
