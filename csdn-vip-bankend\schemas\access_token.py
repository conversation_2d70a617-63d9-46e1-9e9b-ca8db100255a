from typing import Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from models import AccessToken, TokenStatus
from utils.model_pydantic import pydantic_model_creator


# 过滤掉敏感信息的输出模型
AccessTokenOut = pydantic_model_creator(
    AccessToken,
    name="AccessTokenOut",
    exclude=(
        'is_deleted',
    )
)

# 管理员查看的完整信息（包含敏感信息）
AccessTokenAdmin = pydantic_model_creator(
    AccessToken,
    name="AccessTokenAdmin",
    exclude=(
        'is_deleted',
    )
)


class AccessTokenCreate(BaseModel):
    """创建访问令牌"""
    name: Optional[str] = Field(None, description="令牌名称（备注）")
    max_usage_count: Optional[int] = Field(None, description="最大使用次数")
    expire_time: Optional[datetime] = Field(None, description="过期时间")
    max_daily_usage: Optional[int] = Field(None, description="每日最大使用次数")
    max_hourly_usage: Optional[int] = Field(None, description="每小时最大使用次数")
    client_ip: Optional[str] = Field(None, description="绑定的客户端IP（可选）")
    notes: Optional[str] = Field(None, description="备注信息")

    @validator('max_usage_count')
    def validate_max_usage_count(cls, v, values):
        if v is not None and v <= 0:
            raise ValueError('最大使用次数必须大于0')
        return v

    @validator('expire_time')
    def validate_expire_time(cls, v):
        if v is not None and v <= datetime.now():
            raise ValueError('过期时间必须大于当前时间')
        return v

    @validator('max_daily_usage')
    def validate_max_daily_usage(cls, v):
        if v is not None and v <= 0:
            raise ValueError('每日最大使用次数必须大于0')
        return v

    @validator('max_hourly_usage')
    def validate_max_hourly_usage(cls, v):
        if v is not None and v <= 0:
            raise ValueError('每小时最大使用次数必须大于0')
        return v

    class Config:
        schema_extra = {
            'example': {
                'name': '测试令牌',
                'max_usage_count': 1000,
                'expire_time': '2024-12-31T23:59:59',
                'max_daily_usage': 100,
                'max_hourly_usage': 10,
                'client_ip': '*************',
                'notes': '用于测试的令牌'
            }
        }


class AccessTokenUpdate(BaseModel):
    """更新访问令牌"""
    name: Optional[str] = Field(None, description="令牌名称（备注）")
    status: Optional[TokenStatus] = Field(None, description="令牌状态")
    max_usage_count: Optional[int] = Field(None, description="最大使用次数")
    expire_time: Optional[datetime] = Field(None, description="过期时间")
    max_daily_usage: Optional[int] = Field(None, description="每日最大使用次数")
    max_hourly_usage: Optional[int] = Field(None, description="每小时最大使用次数")
    client_ip: Optional[str] = Field(None, description="绑定的客户端IP（可选）")
    notes: Optional[str] = Field(None, description="备注信息")

    @validator('max_usage_count')
    def validate_max_usage_count(cls, v):
        if v is not None and v <= 0:
            raise ValueError('最大使用次数必须大于0')
        return v

    @validator('max_daily_usage')
    def validate_max_daily_usage(cls, v):
        if v is not None and v <= 0:
            raise ValueError('每日最大使用次数必须大于0')
        return v

    @validator('max_hourly_usage')
    def validate_max_hourly_usage(cls, v):
        if v is not None and v <= 0:
            raise ValueError('每小时最大使用次数必须大于0')
        return v


class AccessTokenQuery(BaseModel):
    """查询访问令牌"""
    status: Optional[str] = Field(None, description="令牌状态")
    created_by: Optional[int] = Field(None, description="创建者ID")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")

    @validator('status')
    def validate_status(cls, v):
        if v is None:
            return None
        # 支持字符串到 TokenStatus 的转换
        if isinstance(v, str):
            status_map = {
                'ACTIVE': TokenStatus.ACTIVE,
                'EXPIRED': TokenStatus.EXPIRED,
                'EXHAUSTED': TokenStatus.EXHAUSTED,
                'DISABLED': TokenStatus.DISABLED
            }
            if v in status_map:
                return status_map[v]
            else:
                raise ValueError(f'无效的状态值: {v}')
        return v


class TokenValidationRequest(BaseModel):
    """令牌验证请求"""
    token: str = Field(..., description="访问令牌")
    url: Optional[str] = Field(None, description="要提取的URL")
    client_ip: Optional[str] = Field(None, description="客户端IP")


class TokenValidationResponse(BaseModel):
    """令牌验证响应"""
    valid: bool = Field(..., description="是否有效")
    token_info: Optional[AccessTokenOut] = Field(None, description="令牌信息")
    remaining_count: Optional[int] = Field(None, description="剩余使用次数")
    remaining_time: Optional[str] = Field(None, description="剩余时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class TokenUsageStats(BaseModel):
    """令牌使用统计"""
    total_requests: int = Field(..., description="总请求次数")
    success_requests: int = Field(..., description="成功请求次数")
    failed_requests: int = Field(..., description="失败请求次数")
    blocked_requests: int = Field(..., description="被阻止请求次数")
    success_rate: float = Field(..., description="成功率")
    unique_ips: int = Field(..., description="唯一IP数")
    average_response_time: float = Field(..., description="平均响应时间")
    daily_stats: dict = Field(..., description="每日统计")


class GenerateAccessLinkRequest(BaseModel):
    """生成访问链接请求"""
    token: str = Field(..., description="访问令牌")
    base_url: Optional[str] = Field(None, description="基础URL（不提供则使用默认）")


class GenerateAccessLinkResponse(BaseModel):
    """生成访问链接响应"""
    access_link: str = Field(..., description="生成的访问链接")
    token_info: AccessTokenOut = Field(..., description="令牌信息")
    expires_at: Optional[datetime] = Field(None, description="链接过期时间")
