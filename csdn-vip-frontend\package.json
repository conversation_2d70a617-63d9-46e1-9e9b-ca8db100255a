{"name": "csdn-vip-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.6.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "lucide-vue-next": "^0.539.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^24.2.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "vite": "^7.1.0", "vue-tsc": "^3.0.5"}}