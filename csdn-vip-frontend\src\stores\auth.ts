import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/services/api'
import type { AdminUser, AccessToken, Token } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<AdminUser | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const currentAccessToken = ref<AccessToken | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const hasValidAccessToken = computed(() => !!currentAccessToken.value)

  // Actions
  const login = async (username: string, password: string) => {
    loading.value = true
    error.value = null

    try {
      // Create FormData for OAuth2PasswordRequestForm
      const formData = new FormData()
      formData.append('username', username)
      formData.append('password', password)

      const response = await api.post('/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      const { access_token, token_type } = response.data.result
      token.value = access_token
      if (typeof window !== 'undefined') {
        localStorage.setItem('auth_token', access_token)
      }

      // Set default authorization header
      api.defaults.headers.common['Authorization'] = `${token_type} ${access_token}`

      // Set user as authenticated (skip the test token call)
      user.value = {
        id: 1,
        username: username,
        email: '',
        nickname: '',
        is_active: true,
        is_super: true,
        created_time: new Date().toISOString(),
        updated_time: new Date().toISOString()
      }

      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    currentAccessToken.value = null
    localStorage.removeItem('auth_token')
    delete api.defaults.headers.common['Authorization']
  }



  const validateToken = async (accessToken: string): Promise<Token> => {
    try {
      const response = await api.post('/access-token/validate', {
        token: accessToken
      })

      const tokenInfo = response.data.result.token_info
      currentAccessToken.value = tokenInfo

      // Convert AccessToken to Token format
      const token: Token = {
        id: tokenInfo.id,
        name: tokenInfo.name,
        max_usage: tokenInfo.max_usage_count || 0,
        current_usage: tokenInfo.current_usage_count,
        status: tokenInfo.status,
        expire_time: tokenInfo.expire_time
      }

      return token
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Token validation failed'
      throw err
    }
  }

  const initializeAuth = async () => {
    try {
      // Initialize token from localStorage
      if (typeof window !== 'undefined') {
        const storedToken = localStorage.getItem('auth_token')
        if (storedToken) {
          token.value = storedToken
          api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
          // Set a basic user object to indicate authentication
          user.value = {
            id: 1,
            username: 'admin',
            email: '',
            nickname: '',
            is_active: true,
            is_super: true,
            created_time: new Date().toISOString(),
            updated_time: new Date().toISOString()
          }
        }
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error)
    }
  }

  const updateProfile = async (profileData: Partial<AdminUser>) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.put(`/admin-user/${user.value?.id}`, profileData)
      user.value = { ...user.value, ...response.data.result }
      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Profile update failed'
      return false
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    loading.value = true
    error.value = null
    
    try {
      await api.post('/admin-user/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      })
      return true
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Password change failed'
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    token,
    currentAccessToken,
    loading,
    error,
    
    // Getters
    isAuthenticated,
    hasValidAccessToken,
    
    // Actions
    login,
    logout,
    validateToken,
    initializeAuth,
    updateProfile,
    changePassword
  }
})
