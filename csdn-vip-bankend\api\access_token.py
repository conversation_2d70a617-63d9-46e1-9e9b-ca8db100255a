from models import AccessToken, AdminUser, TokenUsageLog, TokenStatus, UsageType, UsageStatus
from schemas import (
    AccessTokenOut, AccessTokenAdmin, AccessTokenCreate, AccessTokenUpdate,
    AccessTokenQuery, TokenValidationRequest, TokenValidationResponse,
    TokenUsageStats, GenerateAccessLinkRequest, GenerateAccessLinkResponse
)
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from core.auth.base import get_current_active_user
from core.response import ResultResponse
from core.exce.base import UserError
from datetime import datetime, timedelta
import secrets
import string
from urllib.parse import urlencode
from config import settings


access_token_router = APIRouter(
    prefix="/access-token",
    tags=["访问令牌管理"],
)


def generate_token(length: int = 32) -> str:
    """生成随机访问令牌"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


@access_token_router.post(
    "",
    name="Create Access Token",
    summary="创建访问令牌",
    response_model=ResultResponse[AccessTokenAdmin]
)
async def create_access_token(
    token_data: AccessTokenCreate,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[AccessTokenAdmin]:
    """创建新的访问令牌"""
    
    # 生成唯一令牌
    while True:
        token = generate_token()
        existing = await AccessToken.get_or_none(token=token)
        if not existing:
            break
    
    # 创建令牌记录
    access_token = await AccessToken.create(
        token=token,
        name=token_data.name,
        max_usage_count=token_data.max_usage_count,
        expire_time=token_data.expire_time,
        max_daily_usage=token_data.max_daily_usage,
        max_hourly_usage=token_data.max_hourly_usage,
        client_ip=token_data.client_ip,
        notes=token_data.notes,
        created_by=current_user
    )
    
    result = AccessTokenAdmin.from_orm(access_token)
    return ResultResponse[AccessTokenAdmin](result=result)


@access_token_router.get(
    "",
    name="List Access Tokens",
    summary="获取访问令牌列表",
    response_model=ResultResponse[dict]
)
async def list_access_tokens(
    query: AccessTokenQuery = Depends(),
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[dict]:
    """获取访问令牌列表"""
    
    # 构建查询条件
    filters = {}
    if query.status is not None:
        filters['status'] = query.status
    if query.created_by is not None:
        filters['created_by_id'] = query.created_by
    
    # 分页查询（预加载关联的 created_by 用户信息）
    offset = (query.page - 1) * query.page_size
    tokens = await AccessToken.filter(**filters).prefetch_related('created_by').offset(offset).limit(query.page_size).all()

    # 获取总数
    total = await AccessToken.filter(**filters).count()

    # 计算总页数
    pages = (total + query.page_size - 1) // query.page_size

    results = [AccessTokenAdmin.from_orm(token) for token in tokens]

    # 返回分页格式的数据
    pagination_result = {
        "items": results,
        "total": total,
        "page": query.page,
        "size": query.page_size,
        "pages": pages
    }

    return ResultResponse[dict](result=pagination_result)


@access_token_router.get(
    "/{token_id}",
    name="Get Access Token",
    summary="获取访问令牌详情",
    response_model=ResultResponse[AccessTokenAdmin]
)
async def get_access_token(
    token_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[AccessTokenAdmin]:
    """获取访问令牌详情"""
    
    token = await AccessToken.get_or_none(id=token_id).prefetch_related('created_by')
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')
    
    result = AccessTokenAdmin.from_orm(token)
    return ResultResponse[AccessTokenAdmin](result=result)


@access_token_router.put(
    "/{token_id}",
    name="Update Access Token",
    summary="更新访问令牌",
    response_model=ResultResponse[AccessTokenAdmin]
)
async def update_access_token(
    token_id: int,
    token_data: AccessTokenUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[AccessTokenAdmin]:
    """更新访问令牌"""
    
    token = await AccessToken.get_or_none(id=token_id)
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')

    # 更新字段
    update_data = token_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(token, field, value)

    await token.save()

    # 重新获取带关联数据的令牌
    token = await AccessToken.get_or_none(id=token_id).prefetch_related('created_by')
    result = AccessTokenAdmin.from_orm(token)
    return ResultResponse[AccessTokenAdmin](result=result)


@access_token_router.delete(
    "/{token_id}",
    name="Delete Access Token",
    summary="删除访问令牌",
    response_model=ResultResponse[dict]
)
async def delete_access_token(
    token_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[dict]:
    """删除访问令牌"""
    
    token = await AccessToken.get_or_none(id=token_id)
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')
    
    # 软删除
    token.is_deleted = True
    await token.save()
    
    return ResultResponse[dict](result={"message": "访问令牌已删除"})


@access_token_router.post(
    "/validate",
    name="Validate Token",
    summary="验证访问令牌",
    response_model=ResultResponse[TokenValidationResponse]
)
async def validate_token(
    request: TokenValidationRequest,
    http_request: Request
) -> ResultResponse[TokenValidationResponse]:
    """验证访问令牌有效性"""
    
    # 获取客户端IP
    client_ip = request.client_ip or http_request.client.host
    
    # 查找令牌（预加载created_by关系）
    token = await AccessToken.get_or_none(token=request.token).prefetch_related('created_by')
    if not token or token.is_deleted:
        response = TokenValidationResponse(
            valid=False,
            error_message="令牌不存在"
        )
        # 记录失败日志
        if token:
            await TokenUsageLog.log_usage(
                token_id=token.id,
                usage_type=UsageType.AUTH_CHECK,
                status=UsageStatus.FAILED,
                client_ip=client_ip,
                request_url=request.url,
                error_code="TOKEN_NOT_FOUND",
                error_message="令牌不存在"
            )
        return ResultResponse[TokenValidationResponse](result=response)
    
    # 检查令牌有效性
    if not token.is_valid():
        # 更新令牌状态
        if token.expire_time and datetime.now() > token.expire_time:
            token.status = TokenStatus.EXPIRED
        elif token.max_usage_count and token.used_count >= token.max_usage_count:
            token.status = TokenStatus.EXHAUSTED
        await token.save()
        
        response = TokenValidationResponse(
            valid=False,
            token_info=AccessTokenOut.from_orm(token),
            error_message="令牌已失效"
        )
        
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=token.id,
            usage_type=UsageType.AUTH_CHECK,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            request_url=request.url,
            error_code="TOKEN_INVALID",
            error_message="令牌已失效"
        )
        return ResultResponse[TokenValidationResponse](result=response)
    
    # 检查IP绑定
    if token.client_ip and token.client_ip != client_ip:
        response = TokenValidationResponse(
            valid=False,
            token_info=AccessTokenOut.from_orm(token),
            error_message="IP地址不匹配"
        )
        
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=token.id,
            usage_type=UsageType.AUTH_CHECK,
            status=UsageStatus.BLOCKED,
            client_ip=client_ip,
            request_url=request.url,
            error_code="IP_MISMATCH",
            error_message="IP地址不匹配"
        )
        return ResultResponse[TokenValidationResponse](result=response)
    
    # 检查速率限制
    if not await TokenUsageLog.check_rate_limit(
        token.id, client_ip, 1, token.max_hourly_usage
    ):
        response = TokenValidationResponse(
            valid=False,
            token_info=AccessTokenOut.from_orm(token),
            error_message="超出每小时使用限制"
        )
        
        # 记录阻止日志
        await TokenUsageLog.log_usage(
            token_id=token.id,
            usage_type=UsageType.AUTH_CHECK,
            status=UsageStatus.BLOCKED,
            client_ip=client_ip,
            request_url=request.url,
            error_code="HOURLY_LIMIT",
            error_message="超出每小时使用限制"
        )
        return ResultResponse[TokenValidationResponse](result=response)
    
    # 检查每日限制
    if not await TokenUsageLog.check_daily_limit(token.id, token.max_daily_usage):
        response = TokenValidationResponse(
            valid=False,
            token_info=AccessTokenOut.from_orm(token),
            error_message="超出每日使用限制"
        )
        
        # 记录阻止日志
        await TokenUsageLog.log_usage(
            token_id=token.id,
            usage_type=UsageType.AUTH_CHECK,
            status=UsageStatus.BLOCKED,
            client_ip=client_ip,
            request_url=request.url,
            error_code="DAILY_LIMIT",
            error_message="超出每日使用限制"
        )
        return ResultResponse[TokenValidationResponse](result=response)
    
    # 计算剩余次数和时间
    remaining_count = None
    if token.max_usage_count:
        remaining_count = token.max_usage_count - token.used_count
    
    remaining_time = None
    if token.expire_time:
        remaining_time = str(token.expire_time - datetime.now())
    
    response = TokenValidationResponse(
        valid=True,
        token_info=AccessTokenOut.from_orm(token),
        remaining_count=remaining_count,
        remaining_time=remaining_time
    )
    
    # 记录成功日志
    await TokenUsageLog.log_usage(
        token_id=token.id,
        usage_type=UsageType.AUTH_CHECK,
        status=UsageStatus.SUCCESS,
        client_ip=client_ip,
        request_url=request.url
    )
    
    return ResultResponse[TokenValidationResponse](result=response)


@access_token_router.post(
    "/generate-link",
    name="Generate Access Link",
    summary="生成访问链接",
    response_model=ResultResponse[GenerateAccessLinkResponse]
)
async def generate_access_link(
    request: GenerateAccessLinkRequest,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[GenerateAccessLinkResponse]:
    """生成带token的访问链接"""
    
    # 验证令牌存在
    token = await AccessToken.get_or_none(token=request.token).prefetch_related('created_by')
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')
    
    # 构建访问链接
    base_url = request.base_url or "http://localhost:5173"  # 默认前端地址
    # 确保base_url以/结尾，然后添加portal路径
    if not base_url.endswith('/'):
        base_url += '/'
    params = {'token': request.token}
    access_link = f"{base_url}portal?{urlencode(params)}"
    
    response = GenerateAccessLinkResponse(
        access_link=access_link,
        token_info=AccessTokenOut.from_orm(token),
        expires_at=token.expire_time
    )
    
    return ResultResponse[GenerateAccessLinkResponse](result=response)


@access_token_router.get(
    "/{token_id}/stats",
    name="Get Token Usage Stats",
    summary="获取令牌使用统计",
    response_model=ResultResponse[TokenUsageStats]
)
async def get_token_usage_stats(
    token_id: int,
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[TokenUsageStats]:
    """获取令牌使用统计"""
    
    token = await AccessToken.get_or_none(id=token_id)
    if not token or token.is_deleted:
        raise UserError('访问令牌不存在')
    
    # 获取使用统计
    stats_data = await TokenUsageLog.get_usage_stats(token_id, days)
    
    # 计算成功率
    success_rate = 0.0
    if stats_data['total_requests'] > 0:
        success_rate = (stats_data['success_requests'] / stats_data['total_requests']) * 100
    
    stats = TokenUsageStats(
        total_requests=stats_data['total_requests'],
        success_requests=stats_data['success_requests'],
        failed_requests=stats_data['failed_requests'],
        blocked_requests=stats_data['blocked_requests'],
        success_rate=success_rate,
        unique_ips=stats_data['unique_ips'],
        average_response_time=stats_data['average_response_time'],
        daily_stats=stats_data['daily_stats']
    )
    
    return ResultResponse[TokenUsageStats](result=stats)
